package com.ly.titc.pms.member.dubbo.entity.response;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @Author：rui
 * @name：MemberTagConfigInfoResp
 * @Date：2024-11-8 11:37
 * @Filename：MemberTagConfigInfoResp
 */
@Data
public class MemberTagConfigDetailResp {

    /**
     * 自增id
     */
    private Long id;

    /**
     * 主类型 1:集团;2:门店;3:艺龙
     */
    private Integer masterType;

    /**
     * 主类型编码 ELONG 集团编码 门店编码
     */
    private String masterCode;

    /**
     * 标签名称
     */
    private String name;

    /**
     * 标签分类
     */
    private Integer type;

    /**
     * 打标分类 1 自动打标 2 手动达标
     */
    private Integer markType;

    /**
     * 自动删除 0 否 1 是
     */
    private Integer autoDelete;

    /**
     * 满足条件 ALL - 全部  ANY-满足任何一个条件
     */
    private String satisfyPerformType;

    /**
     * 标签描述
     */
    private String remark;

    /**
     * 统计周期
     */
    private Integer cycleType;

    /**
     * 状态；0 无效 1 正常
     */
    private Integer state;

    /**
     * 排序值
     */
    private Integer sort;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 修改人
     */
    private String modifyUser;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 修改时间
     */
    private LocalDateTime gmtModified;

    /**
     * 标签打标规则
     */
    private List<MemberTagMarkRuleResp> markRuleList;

    /**
     * 会员数量
     */
    private Integer memberCount;
}
