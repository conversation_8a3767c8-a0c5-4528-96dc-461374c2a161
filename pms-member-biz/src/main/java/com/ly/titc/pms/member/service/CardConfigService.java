package com.ly.titc.pms.member.service;

import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ly.titc.common.constants.Constant;
import com.ly.titc.pms.member.biz.*;
import com.ly.titc.pms.member.com.utils.WorkerUtil;
import com.ly.titc.pms.member.dal.entity.po.*;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/25 14:30
 */
@Service
public class CardConfigService {

    @Resource
    private CardConfigBiz cardConfigBiz;

    @Resource
    private CardNoRuleBiz cardNoRuleBiz;

    @Resource
    private CardLevelConfigBiz cardLevelConfigBiz;

    @Resource
    private CardLevelUpgradeRuleBiz cardLevelUpgradeRuleBiz;

    @Resource
    private CardLevelRelegationRuleBiz cardLevelRelegationRuleBiz;

    @Resource
    private CardLevelPrivilegeConfigBiz cardLevelPrivilegeConfigBiz;

    @Resource
    private PrivilegeConfigBiz privilegeConfigBiz;



    /**
     * 保存会员卡配置
     *
     * @param cardConfigInfo
     * @param mappings
     * @param cardNoRuleInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveCardConfig(CardConfigInfo cardConfigInfo,
                               List<CardApplicableDataMapping> mappings,
                               CardNoRuleInfo cardNoRuleInfo){
        if (cardConfigInfo.getIsDefault() == Constant.ONE) {
            cardConfigBiz.updateAllIsDefault(cardConfigInfo.getMasterType(), cardConfigInfo.getMasterCode());
        }
        CardConfigInfo cardConfig = cardConfigBiz.getByCardId(cardConfigInfo.getMasterType(), cardConfigInfo.getMasterCode(), cardConfigInfo.getId());
        if (cardConfig == null) {
            mappings.forEach(e -> e.setId(IdUtil.getSnowflake(WorkerUtil.getWorkId(), WorkerUtil.getDataCenterId()).nextId()));
            cardConfigBiz.insert(cardConfigInfo, mappings);
            cardNoRuleBiz.insert(cardNoRuleInfo);
        } else {
            cardConfigBiz.update(cardConfigInfo, mappings);
            cardNoRuleBiz.update(cardNoRuleInfo);
        }
    }

    /**
     * 保存卡等级升级规则
     *
     * @param cardLevelUpgradeRuleInfo
     * @param upgradeRuleDetails
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveCardLevelUpgradeRule(CardLevelUpgradeRuleInfo cardLevelUpgradeRuleInfo,
                                         List<CardLevelUpgradeRuleDetailInfo> upgradeRuleDetails) {
        CardLevelUpgradeRuleInfo ruleInfo = cardLevelUpgradeRuleBiz.getById(cardLevelUpgradeRuleInfo.getMasterType(), cardLevelUpgradeRuleInfo.getMasterCode(), cardLevelUpgradeRuleInfo.getId());
        if (ruleInfo == null) {
            upgradeRuleDetails.forEach(e -> e.setId(IdUtil.getSnowflake(WorkerUtil.getWorkId(), WorkerUtil.getDataCenterId()).nextId()));
            cardLevelUpgradeRuleBiz.insert(cardLevelUpgradeRuleInfo, upgradeRuleDetails);
        } else {
            cardLevelUpgradeRuleBiz.update(cardLevelUpgradeRuleInfo, upgradeRuleDetails);
        }
    }

    /**
     * 保存卡等级保级规则
     *
     * @param cardLevelRelegationRuleInfo
     * @param relegationRuleDetails
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveCardLevelRelegationRule(CardLevelRelegationRuleInfo cardLevelRelegationRuleInfo,
                                            List<CardLevelRelegationRuleDetailInfo> relegationRuleDetails) {
        CardLevelRelegationRuleInfo ruleInfo = cardLevelRelegationRuleBiz.getById(cardLevelRelegationRuleInfo.getMasterType(), cardLevelRelegationRuleInfo.getMasterCode(), cardLevelRelegationRuleInfo.getId());
        if (ruleInfo == null) {
            relegationRuleDetails.forEach(e -> e.setId(IdUtil.getSnowflake(WorkerUtil.getWorkId(), WorkerUtil.getDataCenterId()).nextId()));
            cardLevelRelegationRuleBiz.insert(cardLevelRelegationRuleInfo, relegationRuleDetails);
        } else {
            cardLevelRelegationRuleBiz.update(cardLevelRelegationRuleInfo, relegationRuleDetails);
        }
    }

    /**
     * 保存卡等级配置
     *
     * @param cardLevelConfigInfo
     * @param mappings
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveCardLevelConfig(CardLevelConfigInfo cardLevelConfigInfo, List<CardLevelPrivilegeConfigInfo> mappings) {
        CardLevelConfigInfo cardLevelConfig = cardLevelConfigBiz.getById(cardLevelConfigInfo.getMasterType(), cardLevelConfigInfo.getMasterCode(), cardLevelConfigInfo.getId());
        if (cardLevelConfig == null) {
            cardLevelConfigBiz.insert(cardLevelConfigInfo);
            mappings.forEach(e -> e.setId(IdUtil.getSnowflake(WorkerUtil.getWorkId(), WorkerUtil.getDataCenterId()).nextId()));
            cardLevelPrivilegeConfigBiz.batchInsert(mappings);
        } else {
            cardLevelConfigBiz.update(cardLevelConfigInfo);
            cardLevelPrivilegeConfigBiz.delete(cardLevelConfig.getCardId(), cardLevelConfig.getCardLevel());
            cardLevelPrivilegeConfigBiz.batchInsert(mappings);
        }
    }

    /**
     * 删除会员卡
     *
     * @param cardId
     * @param operator
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteCard(Long cardId, String operator) {
        cardConfigBiz.deleteByCardId(cardId, operator);
        cardLevelPrivilegeConfigBiz.deleteByCardId(cardId, operator);
        cardNoRuleBiz.deleteByCardId(cardId, operator);
        cardLevelConfigBiz.deleteByCardId(cardId, operator);
        cardLevelUpgradeRuleBiz.deleteByCardId(cardId, operator);
        cardLevelRelegationRuleBiz.deleteByCardId(cardId, operator);
    }

    @Transactional(rollbackFor = Exception.class)
    public void deleteCardLevel(Long cardId, Integer cardLevel, String operator) {
        cardLevelConfigBiz.deleteByCardId(cardId, cardLevel, operator);
        cardLevelPrivilegeConfigBiz.deleteByCardId(cardId, cardLevel, operator);
        cardLevelUpgradeRuleBiz.deleteByCardId(cardId, cardLevel, operator);
        cardLevelRelegationRuleBiz.deleteByCardId(cardId, cardLevel, operator);
    }
}
