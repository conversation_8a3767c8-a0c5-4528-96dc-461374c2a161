package com.ly.titc.pms.member.mediator.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.ly.titc.common.exceptions.ServiceException;
import com.ly.titc.common.factory.RedisFactory;
import com.ly.titc.pms.member.biz.*;
import com.ly.titc.pms.member.com.constant.CommonConstant;
import com.ly.titc.pms.member.com.enums.MemberRelatedConfigEnum;
import com.ly.titc.pms.member.com.enums.RespCodeEnum;
import com.ly.titc.pms.member.com.utils.CommonUtil;
import com.ly.titc.pms.member.dal.entity.po.*;
import com.ly.titc.pms.member.mediator.converter.ConfigInitMedConverter;
import com.ly.titc.pms.member.mediator.service.ConfigInitMedService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025/6/26 22:51
 */
@Slf4j
@Service
public class ConfigInitMedServiceImpl implements ConfigInitMedService {

    @Resource
    private ConfigInitMedConverter configInitMedConverter;

    @Resource
    private PrivilegeConfigBiz privilegeConfigBiz;

    @Resource
    private MemberTagConfigBiz memberTagConfigBiz;

    @Resource
    private CardConfigBiz cardConfigBiz;

    @Resource
    private CardNoRuleBiz cardNoRuleBiz;

    @Resource
    private CardLevelConfigBiz cardLevelConfigBiz;

    @Resource
    private CardLevelPrivilegeConfigBiz cardLevelPrivilegeConfigBiz;

    @Resource
    private MemberRelatedConfigBiz memberRelatedConfigBiz;

    @Resource
    private RedisFactory redisFactory;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void initCardConfig(Integer masterType, String masterCode) {

        String idempotentKey = CommonUtil.concat(CommonConstant.MEMBER_CONFIG_INIT_IDEMPOTENT_PREFIX, masterType, masterCode);
        Boolean result = redisFactory.setNx(idempotentKey, 6, "1");
        //处理中...
        if (!result) {
            log.warn("this member config init  is processing...masterType:{};masterCode:{}", masterType, masterCode);
            throw new ServiceException(RespCodeEnum.CODE_1000);
        }
        try {
            List<CardConfigInfo> cardConfigInfos = cardConfigBiz.listByMaster(masterType, masterCode, null);
            if (CollectionUtil.isNotEmpty(cardConfigInfos)) {
                return;
            }
            // ==============权益=================
            // 默认公共的
            List<PrivilegeConfigInfo> infos = new ArrayList<>(configInitMedConverter.initCommonPrivilege(masterType, masterCode));
            List<PrivilegeApplicableDataMapping> commonPrivilegeMappingList = new ArrayList<>();
            // 价格权益
            PrivilegeConfigInfo pricePrivilege = configInitMedConverter.initPricePrivilege(masterType, masterCode);
            infos.add(pricePrivilege);
            commonPrivilegeMappingList.add(configInitMedConverter.initPricePrivilegeApplicableDataMapping(pricePrivilege.getId()));
            // 预定保留
            PrivilegeConfigInfo reservePrivilege = configInitMedConverter.initReservePrivilege(masterType, masterCode);
            infos.add(reservePrivilege);
            commonPrivilegeMappingList.add(configInitMedConverter.initReservePrivilegeApplicableDataMapping(reservePrivilege.getId()));
            // 延迟退房
            PrivilegeConfigInfo delayPrivilege = configInitMedConverter.initDelayPrivilege(masterType, masterCode);
            infos.add(delayPrivilege);
            commonPrivilegeMappingList.add(configInitMedConverter.initDelayPrivilegeApplicableDataMapping(delayPrivilege.getId()));
            privilegeConfigBiz.batchAdd(infos, commonPrivilegeMappingList);
            // ==============标签=================
            memberTagConfigBiz.batchAdd(configInitMedConverter.initMemberTag(masterType, masterCode));
            // ==============会员卡=================
            CardConfigInfo cardConfigInfo = configInitMedConverter.initMemberCard(masterType, masterCode);
            cardConfigBiz.insert(cardConfigInfo, null);
            // ==============卡生成规则=================
            cardNoRuleBiz.insert(configInitMedConverter.initMemberCardNoRule(cardConfigInfo.getId()));
            // ==============会员等级初始化=================
            List<CardLevelConfigInfo> cardLevelConfigInfos = configInitMedConverter.initMemberCardLevel(masterType, masterCode, cardConfigInfo.getId());
            cardLevelConfigBiz.batchAdd(cardLevelConfigInfos);
            // ==============会员卡权益关联=================
            cardLevelPrivilegeConfigBiz.batchInsert(configInitMedConverter.initMemberCardPrivilege(cardLevelConfigInfos, pricePrivilege.getId()));
            // ==============必填项配置=================
            MemberRelatedConfigInfo memberRelatedConfigInfo = configInitMedConverter.initMemberRelatedConfig(masterType, masterCode);
            memberRelatedConfigBiz.insert(memberRelatedConfigInfo);
            // 收银设置 TODO
        } finally {
            redisFactory.del(idempotentKey);
        }

    }
}
