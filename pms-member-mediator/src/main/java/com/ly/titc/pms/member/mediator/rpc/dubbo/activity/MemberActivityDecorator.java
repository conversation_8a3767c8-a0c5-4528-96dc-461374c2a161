package com.ly.titc.pms.member.mediator.rpc.dubbo.activity;

import com.alibaba.fastjson.JSON;
import com.ly.titc.common.entity.Pageable;
import com.ly.titc.common.entity.Response;
import com.ly.titc.pms.member.com.enums.StateEnum;
import com.ly.titc.pms.spm.dubbo.entity.request.activity.DeleteActivityReq;
import com.ly.titc.pms.spm.dubbo.entity.request.activity.UpdateActivityStateReq;
import com.ly.titc.pms.spm.dubbo.entity.request.activity.member.*;
import com.ly.titc.pms.spm.dubbo.entity.response.activity.member.*;
import com.ly.titc.pms.spm.dubbo.interfaces.MemberActivityDubboService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import javax.validation.Valid;
import java.util.List;
import java.util.Objects;

/**
 * 会员活动
 *
 * <AUTHOR>
 * @date 2024/12/31 14:19
 */
@Slf4j
@Component
public class MemberActivityDecorator {

    @DubboReference(group = "${spm-dsf-dubbo-group}")
    private MemberActivityDubboService memberActivityDubboService;

    /**
     * 保存售卡活动
     */
    public String saveSaleCardActivity(SaveSaleCardActivityReq req){
        log.info("保存售卡活动入参数:{}", JSON.toJSONString(req));
        return Response.getValidateData(memberActivityDubboService.saveSaleCardActivity(req));
    }

    /**
     * 查询售卡活动
     */
    public MemberCardSaleActivityDetailResp getSaleCardActivity(GetMemberActivityReq req){
        log.info("查询售卡活动入参数:{}", JSON.toJSONString(req));
        return Response.getValidateData(memberActivityDubboService.getSaleCardActivity(req));
    }

    /**
     * 保存升级活动
     */
    public String saveUpgradeActivity(@Valid SaveUpgradeActivityReq req){
        log.info("保存升级活动入参数:{}", JSON.toJSONString(req));
        return Response.getValidateData(memberActivityDubboService.saveUpgradeActivity(req));
    }

    /**
     * 查询升级活动
     */
    public MemberUpgradeActivityDetailResp getUpgradeActivity(@Valid GetMemberActivityReq req){
        log.info("查询升级活动入参数:{}", JSON.toJSONString(req));
        return Response.getValidateData(memberActivityDubboService.getUpgradeActivity(req));
    }

    /**
     * 保存储值活动
     */
    public String saveRechargeActivity(@Valid SaveRechargeActivityReq req){
        log.info("保存储值活动入参数:{}", JSON.toJSONString(req));
        return Response.getValidateData(memberActivityDubboService.saveRechargeActivity(req));
    }


    /**
     * 查询充值活动
     */
    public MemberRechargeActivityDetailResp getRechargeActivity(GetMemberActivityReq req){
        log.info("查询储值活动入参数:{}", JSON.toJSONString(req));
        return Response.getValidateData(memberActivityDubboService.getRechargeActivity(req));
    }

    /**
     * 保存积分活动
     */
    public String savePointsActivity(SavePointsActivityReq req){
        log.info("保存积分活动入参数:{}", JSON.toJSONString(req));
        return Response.getValidateData(memberActivityDubboService.savePointsActivity(req));
    }

    /**
     * 查询积分活动
     */
    public MemberPointActivityDetailResp getPointsActivity(@Valid GetMemberActivityReq req){
        log.info("查询积分活动入参数:{}", JSON.toJSONString(req));
        return Response.getValidateData(memberActivityDubboService.getPointsActivity(req));
    }

    /**
     * 查询售卡活动可售卡
     */
    public List<MemberSellableCardResp> listSellableCard(GetSellableCardReq req){
        log.info("查询售卡活动可售卡入参:{}", JSON.toJSONString(req));
        return Response.getValidateData(memberActivityDubboService.listSellableCard(req));
    }

    /**
     * 启用
     */
    public void enable(UpdateActivityStateReq req) {
        req.setState(StateEnum.VALID.getState());
        Response<String> response = memberActivityDubboService.updateState(req);
        Response.getValidateData(response);
    }

    /**
     * 停用
     */
    public void disable(UpdateActivityStateReq req) {
        req.setState(StateEnum.NO_VALID.getState());
        Response<String> response = memberActivityDubboService.updateState(req);
        Response.getValidateData(response);
    }

    /**
     * 删除
     */
    public void delete(DeleteActivityReq req){
        Response<String> response = memberActivityDubboService.delete(req);
        Response.getValidateData(response);
    }

    /**
     * 分页列表
     */
    public Pageable<MemberActivityBaseResp> pageMemberActivity(@Valid PageMemberActivityReq req){
        Response<Pageable<MemberActivityBaseResp>> response = memberActivityDubboService.pageMemberActivity(req);
        return Response.getValidateData(response);
    }

    /**
     * 根据条件查询命中的售卡活动
     */
    public MemberCardSaleActivityDetailResp getActiveSaleCardActivity(GetActiveMemberActivityReq req){
        Response<MemberCardSaleActivityDetailResp> response = memberActivityDubboService.getActiveSaleCardActivity(req);
        return Response.getValidateData(response);
    }

    /**
     * 根据条件查询命中的升级活动
     */
    public MemberUpgradeActivityDetailResp getActiveUpgradeActivity(@Valid GetActiveMemberActivityReq req){
        Response<MemberUpgradeActivityDetailResp> response = memberActivityDubboService.getActiveUpgradeActivity(req);
        return Response.getValidateData(response);
    }

    /**
     * 根据条件查询命中的储值活动
     */
    public MemberRechargeActivityDetailResp getActiveRechargeActivity(@Valid GetActiveMemberActivityReq req){
        Response<MemberRechargeActivityDetailResp> response = memberActivityDubboService.getActiveRechargeActivity(req);
        return Response.getValidateData(response);
    }

    /**
     * 根据条件查询命中的积分活动
     */
    public MemberPointActivityDetailResp getActivePointsActivity(@Valid GetActiveMemberActivityReq req){
        Response<MemberPointActivityDetailResp> response = memberActivityDubboService.getActivePointsActivity(req);
        return Response.getValidateData(response);
    }

    /**
     * 判断当前活动内容是否满足当前条件
     */
    public Boolean judgeAvailableMemberActivity(String blocCode,String activityCode,String itemCode,String memberNo,Long cardId,Integer memberLevel){
        judgeAvailableMemberActivityReq req = new judgeAvailableMemberActivityReq();
        req.setBlocCode(blocCode);
        req.setActivityCode(activityCode);
        req.setItemCode(itemCode);
        req.setMemberNo(memberNo);
        if(!Objects.isNull(cardId)){
            req.setCardId(String.valueOf(cardId));
        }
        req.setMemberLevel(memberLevel);
        Response<Boolean> response = memberActivityDubboService.judgeAvailableMemberActivity(req);
        return Response.getValidateData(response);
    }

    /**
     * 获取优惠信息
     */
    public List<DiscountBenefitResp> listDiscountBenefit(String blocCode,String activityCode,String itemCode){
        GetDiscountBenefitReq req = new GetDiscountBenefitReq();
        req.setActivityCode(activityCode);
        req.setItemCode(itemCode);
        req.setBlocCode(blocCode);
        Response<List<DiscountBenefitResp>> response = memberActivityDubboService.listDiscountBenefit(req);
        return Response.getValidateData(response);
    }
}
