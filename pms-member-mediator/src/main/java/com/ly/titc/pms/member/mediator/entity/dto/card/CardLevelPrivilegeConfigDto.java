package com.ly.titc.pms.member.mediator.entity.dto.card;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @Author：rui
 * @name：MemberCardPrivilegeConfigResp
 * @Date：2024-11-13 19:45
 * @Filename：MemberCardPrivilegeConfigResp
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CardLevelPrivilegeConfigDto implements Serializable {

    /**
     * 主键
     */
    private Long id;

    /**
     * 会员卡ID
     */
    private Long cardId;

    /**
     * 会员卡等级
     */
    private Integer cardLevel;

    /**
     * 权益ID
     */
    private Long privilegeId;

    /**
     * 权益类型
     */
    private Integer type;

    /**
     * 权益名称
     */
    private String name;

    /**
     * 权益描述
     */
    private String description;

    /**
     * 权益说明
     */
    private String instruction;

    /**
     * 权益的值 （只有指定使用范围的时候返回）
     */
    private String value;


    /**
     * 权益值类型 1.仅作展示 2 价格折扣 3 预定保留 4 延迟退房
     */
    private Integer classification;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 状态
     */
    private Integer state;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 修改人
     */
    private String modifyUser;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 修改时间
     */
    private LocalDateTime gmtModified;

}
