package com.ly.titc.pms.member.mediator.handler.schedule;

import com.alibaba.fastjson.JSONObject;
import com.ly.titc.pms.member.asset.dubbo.entity.response.point.MemberPointPeriodResp;
import com.ly.titc.pms.member.asset.dubbo.entity.response.point.MemberTotalPointResp;
import com.ly.titc.pms.member.asset.dubbo.entity.response.store.MemberStorePeriodResp;
import com.ly.titc.pms.member.biz.MemberCardLevelChangeRecordBiz;
import com.ly.titc.pms.member.biz.MemberCheckInRecordBiz;
import com.ly.titc.pms.member.biz.MemberEventHappenInfoBiz;
import com.ly.titc.pms.member.com.constant.TurboMqTopic;
import com.ly.titc.pms.member.com.enums.CalculateTypeEnum;
import com.ly.titc.pms.member.com.lang.TwoTuple;
import com.ly.titc.pms.member.com.utils.ConfigCenterUtil;
import com.ly.titc.pms.member.dal.entity.po.MemberCardLevelChangeRecord;
import com.ly.titc.pms.member.dal.entity.po.MemberCheckInRecord;
import com.ly.titc.pms.member.dal.entity.po.MemberEventHappenInfo;
import com.ly.titc.pms.member.entity.bo.PageMemberCheckInParamBo;
import com.ly.titc.pms.member.mediator.entity.dto.schedule.BaseCheckDto;
import com.ly.titc.pms.member.mediator.entity.dto.schedule.MemberScheduleMqDto;
import com.ly.titc.pms.member.mediator.entity.dto.schedule.ScheduleDataDto;
import com.ly.titc.pms.member.mediator.rpc.dubbo.asset.AssetDecorator;
import com.ly.titc.springboot.mq.producer.TurboMQProducer;
import com.ly.watcher.common.utils.CollectionUtils;
import com.ly.titc.pms.member.com.utils.PageUtil;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.function.Function;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.ly.titc.pms.member.com.constant.TurboMqTopicTag.MEMBER_EVENT;
import static com.ly.titc.pms.member.com.constant.TurboMqTopicTag.MEMBER_SCHEDULE;

/**
 * @Author：rui
 * @name：AbstractScheduleHandler
 * @Date：2024-11-21 14:29
 * @Filename：AbstractScheduleHandler
 */
@Slf4j
public abstract class AbstractScheduleHandler<Data extends ScheduleDataDto> {

    @Resource
    protected MemberCardLevelChangeRecordBiz memberCardLevelChangeRecordBiz;

    @Resource
    protected MemberCheckInRecordBiz checkInRecordBiz;

    @Resource
    protected MemberEventHappenInfoBiz memberEventHappenInfoBiz;

    @Resource(type = TurboMQProducer.class)
    private TurboMQProducer producer;

    @Resource
    private AssetDecorator assetDecorator;


    public void doSchedule() {
        // 定义一个线程池组
        ExecutorService executor = new ThreadPoolExecutor(
                Runtime.getRuntime().availableProcessors() * 5,
                Runtime.getRuntime().availableProcessors() * 5,
                10L,
                TimeUnit.SECONDS,
                new LinkedBlockingQueue<>()
        );

        List<String> blocCodes = ConfigCenterUtil.listBloc();
        for (String blocCode : blocCodes) {
            TwoTuple<Integer, String> masterByBloc = ConfigCenterUtil.getMasterByBloc(blocCode);
            Integer masterType = masterByBloc.getFirst();
            String masterCode = masterByBloc.getSecond();
            // 在内部执行事务代码快
            executor.execute(() -> doScheduleMainByMq(masterType, masterCode));
        }
    }

    public abstract void doScheduleMain(Integer masterType, String masterCode, List<Data> dataList, List<String> memberNos, List<MemberCardLevelChangeRecord> records);

    protected List<String> getNeedHandleMemberList(Integer masterType, String masterCode) {
        return memberEventHappenInfoBiz.listInYesterday(masterType, masterCode)
                .stream()
                .map(MemberEventHappenInfo::getMemberNo)
                .distinct()
                .collect(Collectors.toList());
    }

    public void doScheduleMain(Integer masterType, String masterCode, List<Data> dataList) {
        List<String> memberNo = getNeedHandleMemberList(masterType, masterCode);
        if (CollectionUtils.isEmpty(memberNo)) {
            log.info("没有需要处理的会员");
            return;
        }
        List<MemberCardLevelChangeRecord> records = records(masterType, masterCode);
        if (CollectionUtils.isEmpty(records)) {
            return;
        }
        // 获取所有符合条件的会员
        List<String> memberNos = records.stream().map(MemberCardLevelChangeRecord::getMemberNo).distinct().collect(Collectors.toList());

        // 分批处理，每批处理1000个会员
        int batchSize = 1000;
        int totalBatches = (int) Math.ceil((double) memberNos.size() / batchSize);

        for (int i = 0; i < totalBatches; i++) {
            int start = i * batchSize;
            int end = Math.min(start + batchSize, memberNos.size());
            List<String> batchMemberNos = memberNos.subList(start, end);
            doScheduleMain(masterType, masterCode, dataList, batchMemberNos, records);
        }
    }

    public void doScheduleMainByMq(Integer masterType, String masterCode) {
        List<String> memberNo = getNeedHandleMemberList(masterType, masterCode);
        if (CollectionUtils.isEmpty(memberNo)) {
            log.info("没有需要处理的会员");
            return;
        }
        for (String s : memberNo) {
            MemberScheduleMqDto msg = new MemberScheduleMqDto();
            msg.setMemberNo(s);
            msg.setMasterCode(masterCode);
            msg.setMasterType(masterType);
            msg.setAction(getAction());
            String str = JSONObject.toJSONString(msg);
            try {
                producer.sendMsgWithTag(TurboMqTopic.PMS_MEMBER_BPS_TOPIC, MEMBER_SCHEDULE, str);
            } catch (Exception e) {
                log.error("发送会员定时任务失败, topic:{}, tag:{}, msg:{}", TurboMqTopic.PMS_MEMBER_BPS_TOPIC, MEMBER_SCHEDULE, str);
            }
        }

    }

    public abstract void process(Integer masterType, String masterCode, String memberNo);

    /**
     * 获取类型
     *
     * @return
     */
    public abstract Integer getAction();

    public AbstractScheduleHandler() {
        ScheduleHandlerFactory.putHandler(this.getAction(), this);
    }

    protected AbstractScheduleHandler getHandler() {
        return ScheduleHandlerFactory.getHandler(this.getAction());
    }

    protected List<MemberCardLevelChangeRecord> records(Integer masterType, String masterCode) {
        return memberCardLevelChangeRecordBiz.getLevelChangeRecordByMaster(masterType, masterCode);
    }

    /**
     * 根据masterType，masterCode 以及时间范围，以及memberNoList 获取会员积分记录
     */
    protected List<BaseCheckDto> getMemberPointRecordByMemberNo(Integer masterType, String masterCode, List<String> memberNoList, LocalDateTime start, LocalDateTime end) {
        // 将LocalDateTime转换为String格式，因为AssetDecorator的方法需要String参数
        String startDate = start.toLocalDate().toString() + " 00:00:00";
        String endDate = end.toLocalDate().toString() + " 23:59:59";
        // 调用资产服务获取会员积分统计数据
        MemberPointPeriodResp pointPeriodResp = assetDecorator.getTotalAccountPointsPeriodList(memberNoList, startDate, endDate);
        // 如果返回结果为空或没有数据，返回空列表
        if (pointPeriodResp == null || pointPeriodResp.getMemberPointPeriodList() == null) {
            return new ArrayList<>();
        }
        // 将积分数据转换为BaseCheckDto列表
        return pointPeriodResp.getMemberPointPeriodList().stream().map(item -> {
            BaseCheckDto dto = new BaseCheckDto();
            dto.setMemberNo(item.getMemberNo());
            // 设置总积分余额，如果为null则默认为0
            dto.setTotalScoreBalance(item.getTotalScore() != null ? item.getTotalScore() : 0);
            return dto;
        }).collect(Collectors.toList());
    }

    /**
     * 根据masterType，masterCode 以及时间范围，以及memberNoList 获取会员消费金额记录
     */
    protected List<BaseCheckDto> getMemberConsumptionRecordByMemberNo(Integer masterType, String masterCode, List<String> memberNoList, LocalDateTime start, LocalDateTime end) {
        List<BaseCheckDto> resultList = new ArrayList<>();
        // 如果会员列表为空，直接返回空列表
        if (CollectionUtils.isEmpty(memberNoList)) {
            return resultList;
        }
        // 先获取储值消费金额
        List<BaseCheckDto> rechargeList = getMemberRechargeRecordByMemberNo(masterType, masterCode, memberNoList, start, end);
        Map<String, BaseCheckDto> rechargeMap = rechargeList.stream()
                .collect(Collectors.toMap(BaseCheckDto::getMemberNo, dto -> dto));
        // 将LocalDateTime转换为String格式进行查询
        String startDate = start.toLocalDate().toString();
        String endDate = end.toLocalDate().toString();
        // 为每个会员查询入住记录并计算消费金额
        for (String memberNo : memberNoList) {
            try {
                // 使用分页查询方法获取该会员在指定时间范围内的入住记录
                PageMemberCheckInParamBo paramBo = new PageMemberCheckInParamBo();
                paramBo.setMemberNo(memberNo)
                       .setCheckInBeginTime(startDate)
                       .setCheckInEndTime(endDate);
                // 查询所有符合条件的入住记录
                List<MemberCheckInRecord> checkInRecords = PageUtil.queryAll((int pageIndex, int pageSize) ->
                        checkInRecordBiz.pageCheckInRecord(paramBo.setPageIndex(pageIndex).setPageSize(pageSize)), null);
                BaseCheckDto dto = new BaseCheckDto();
                dto.setMemberNo(memberNo);
                // 计算入住消费金额：房费 + 其他费用
                BigDecimal checkInExpense = BigDecimal.ZERO;
                if (!CollectionUtils.isEmpty(checkInRecords)) {
                    checkInExpense = checkInRecords.stream()
                            .map(record -> {
                                BigDecimal roomRate = record.getRoomRate() != null ? record.getRoomRate() : BigDecimal.ZERO;
                                BigDecimal otherRate = record.getOtherRate() != null ? record.getOtherRate() : BigDecimal.ZERO;
                                return roomRate.add(otherRate);
                            })
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                }

                // 获取储值消费金额
                BigDecimal rechargeExpense = BigDecimal.ZERO;
                BaseCheckDto rechargeDto = rechargeMap.get(memberNo);
                if (rechargeDto != null && rechargeDto.getTotalCapitalAmount() != null) {
                    rechargeExpense = rechargeDto.getTotalCapitalAmount();
                }
                // 总消费金额 = 入住消费 + 储值消费
                BigDecimal totalExpense = checkInExpense.add(rechargeExpense);
                dto.setExpenseAmount(totalExpense);
                resultList.add(dto);
            } catch (Exception e) {
                // 记录异常但继续处理其他会员
                log.warn("查询会员消费记录失败, memberNo: {}, error: {}", memberNo, e.getMessage());
                // 即使查询失败，也添加一个默认的记录
                BaseCheckDto dto = new BaseCheckDto();
                dto.setMemberNo(memberNo);
                dto.setExpenseAmount(BigDecimal.ZERO);
                // 尝试获取储值信息
                BaseCheckDto rechargeDto = rechargeMap.get(memberNo);
                if (rechargeDto != null) {
                    dto.setTotalScoreBalance(rechargeDto.getTotalScoreBalance());
                    dto.setTotalCapitalAmount(rechargeDto.getTotalCapitalAmount());
                    dto.setExpenseAmount(rechargeDto.getTotalCapitalAmount() != null ?
                            rechargeDto.getTotalCapitalAmount() : BigDecimal.ZERO);
                }
                resultList.add(dto);
            }
        }
        return resultList;
    }

    /**
     * 根据masterType，masterCode 以及时间范围，以及memberNoList 获取会员充值金额
     */
    protected List<BaseCheckDto> getMemberRechargeRecordByMemberNo(Integer masterType, String masterCode, List<String> memberNoList, LocalDateTime start, LocalDateTime end) {
        String startDate = start.toLocalDate().toString() + " 00:00:00";
        String endDate = end.toLocalDate().toString() + " 23:59:59";
        MemberStorePeriodResp resp = assetDecorator.getMemeberStorePeriodList(memberNoList, startDate, endDate);
        if (resp == null || resp.getMemberStorePeriodList() == null) {
            return new ArrayList<>();
        }
        // 将积分数据转换为BaseCheckDto列表
        return resp.getMemberStorePeriodList().stream().map(item -> {
            BaseCheckDto dto = new BaseCheckDto();
            dto.setMemberNo(item.getMemberNo());
            dto.setTotalCapitalAmount(item.getTotalAmountResp() != null ? item.getTotalAmountResp().getTotalCapitalAmount() : BigDecimal.ZERO);
            return dto;
        }).collect(Collectors.toList());
    }

    /**
     * 批量处理会员记录的工具方法
     * @param memberNoList 会员编号列表
     * @param start 开始时间
     * @param end 结束时间
     * @param processor 处理函数，接收会员编号列表和时间范围，返回BaseCheckDto列表
     * @param batchSize 批处理大小，默认50
     * @return 处理结果列表
     */
    private List<BaseCheckDto> processMemberRecordsBatch(List<String> memberNoList, LocalDateTime start, LocalDateTime end,
                                                        BatchProcessor processor, int batchSize) {
        List<BaseCheckDto> resultList = new ArrayList<>();

        // 如果会员列表为空，直接返回空列表
        if (CollectionUtils.isEmpty(memberNoList)) {
            return resultList;
        }

        // 将LocalDateTime转换为String格式进行查询
        String startDate = start.toLocalDate().toString() + " 00:00:00";
        String endDate = end.toLocalDate().toString() + " 23:59:59";

        // 分批处理会员列表
        for (int i = 0; i < memberNoList.size(); i += batchSize) {
            int endIndex = Math.min(i + batchSize, memberNoList.size());
            List<String> batchMemberNos = memberNoList.subList(i, endIndex);
            try {
                List<BaseCheckDto> batchResults = processor.process(batchMemberNos, startDate, endDate);
                resultList.addAll(batchResults);
            } catch (Exception e) {
                log.warn("批量处理会员记录失败, memberNos: {}, error: {}", batchMemberNos, e.getMessage());
                // 为失败的批次创建默认记录
                for (String memberNo : batchMemberNos) {
                    BaseCheckDto dto = new BaseCheckDto();
                    dto.setMemberNo(memberNo);
                    resultList.add(dto);
                }
            }
        }
        return resultList;
    }

    /**
     * 批量处理会员记录的工具方法（使用默认批处理大小50）
     */
    private List<BaseCheckDto> processMemberRecordsBatch(List<String> memberNoList, LocalDateTime start, LocalDateTime end,
                                                        BatchProcessor processor) {
        return processMemberRecordsBatch(memberNoList, start, end, processor, 50);
    }

    /**
     * 批处理函数式接口
     */
    @FunctionalInterface
    private interface BatchProcessor {
        List<BaseCheckDto> process(List<String> memberNos, String startDate, String endDate) throws Exception;
    }

    /**
     * 根据masterType，masterCode 以及时间范围，以及memberNoList 获取会员入住次数
     */
    protected List<BaseCheckDto> getMemberCheckoutRecordByMemberNo(Integer masterType, String masterCode, List<String> memberNoList, LocalDateTime start, LocalDateTime end) {
        return processMemberRecordsBatch(memberNoList, start, end, (memberNos, startDate, endDate) -> {
            // 批量查询入住记录
            List<MemberCheckInRecord> allRecords = checkInRecordBiz.listByMemberNosAndDateRange(memberNos, startDate, endDate);
            // 按会员编号分组统计入住次数
            Map<String, Long> memberCheckInCountMap = allRecords.stream()
                    .collect(Collectors.groupingBy(MemberCheckInRecord::getMemberNo, Collectors.counting()));
            // 构建结果列表
            return memberNos.stream().map(memberNo -> {
                BaseCheckDto dto = new BaseCheckDto();
                dto.setMemberNo(memberNo);
                dto.setCheckInCount(memberCheckInCountMap.getOrDefault(memberNo, 0L).intValue());
                return dto;
            }).collect(Collectors.toList());
        });
    }

    /**
     * 根据masterType，masterCode 以及时间范围，以及memberNoList 获取会员入住房晚
     */
    protected List<BaseCheckDto> getMemberStayRecordByMemberNo(Integer masterType, String masterCode, List<String> memberNoList, LocalDateTime start, LocalDateTime end) {
        return processMemberRecordsBatch(memberNoList, start, end, (memberNos, startDate, endDate) -> {
            // 批量查询入住记录
            List<MemberCheckInRecord> allRecords = checkInRecordBiz.listByMemberNosAndDateRange(memberNos, startDate, endDate);

            // 按会员编号分组统计房晚数
            Map<String, Integer> memberRoomNightsMap = allRecords.stream()
                    .collect(Collectors.groupingBy(MemberCheckInRecord::getMemberNo,
                            Collectors.summingInt(MemberCheckInRecord::getRoomNights)));

            // 构建结果列表
            return memberNos.stream().map(memberNo -> {
                BaseCheckDto dto = new BaseCheckDto();
                dto.setMemberNo(memberNo);
                dto.setRoomNights(memberRoomNightsMap.getOrDefault(memberNo, 0));
                return dto;
            }).collect(Collectors.toList());
        });
    }

    /**
     * 根据masterType，masterCode 以及时间范围，以及memberNoList 获取会员未入住天数
     */
    protected List<BaseCheckDto> getMemberUnstayRecordByMemberNo(Integer masterType, String masterCode, List<String> memberNoList, LocalDateTime start, LocalDateTime end) {
        return processMemberRecordsBatch(memberNoList, start, end, (memberNos, startDate, endDate) -> {
            // 批量查询会员最近一次入住记录
            List<MemberCheckInRecord> lastRecords = checkInRecordBiz.listLastByMemberNos(memberNos);

            // 按会员编号建立映射
            Map<String, MemberCheckInRecord> memberLastRecordMap = lastRecords.stream()
                    .collect(Collectors.toMap(MemberCheckInRecord::getMemberNo, record -> record));

            LocalDate currentDate = LocalDate.now();

            // 构建结果列表
            return memberNos.stream().map(memberNo -> {
                BaseCheckDto dto = new BaseCheckDto();
                dto.setMemberNo(memberNo);

                MemberCheckInRecord lastRecord = memberLastRecordMap.get(memberNo);
                if (lastRecord != null && lastRecord.getCheckOutDate() != null) {
                    try {
                        // 解析最后一次离店日期
                        LocalDate lastCheckOutDate = LocalDate.parse(lastRecord.getCheckOutDate());
                        // 计算未入住天数（当前日期 - 最后离店日期）
                        long unstayDays = ChronoUnit.DAYS.between(lastCheckOutDate, currentDate);
                        // 确保未入住天数不为负数
                        dto.setUnstayDays((int) Math.max(0, unstayDays));
                    } catch (Exception e) {
                        log.warn("解析离店日期失败, memberNo: {}, checkOutDate: {}", memberNo, lastRecord.getCheckOutDate());
                        dto.setUnstayDays(0);
                    }
                } else {
                    // 如果没有入住记录，设置为一个较大的值
                    dto.setUnstayDays(Integer.MAX_VALUE);
                }

                return dto;
            }).collect(Collectors.toList());
        });
    }

    /**
     * 根据masterType，masterCode 以及时间范围，以及memberNoList 获取平均房费
     */
    protected List<BaseCheckDto> getMemberAvgRoomFeeRecordByMemberNo(Integer masterType, String masterCode, List<String> memberNoList, LocalDateTime start, LocalDateTime end) {
        return processMemberRecordsBatch(memberNoList, start, end, (memberNos, startDate, endDate) -> {
            // 批量查询入住记录
            List<MemberCheckInRecord> allRecords = checkInRecordBiz.listByMemberNosAndDateRange(memberNos, startDate, endDate);

            // 按会员编号分组计算平均房费
            Map<String, List<MemberCheckInRecord>> memberRecordsMap = allRecords.stream()
                    .collect(Collectors.groupingBy(MemberCheckInRecord::getMemberNo));

            // 构建结果列表
            return memberNos.stream().map(memberNo -> {
                BaseCheckDto dto = new BaseCheckDto();
                dto.setMemberNo(memberNo);

                List<MemberCheckInRecord> memberRecords = memberRecordsMap.get(memberNo);
                if (memberRecords != null && !memberRecords.isEmpty()) {
                    // 计算总房费和总房晚数
                    BigDecimal totalRoomFee = memberRecords.stream()
                            .map(record -> record.getRoomRate() != null ? record.getRoomRate() : BigDecimal.ZERO)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);

                    int totalRoomNights = memberRecords.stream()
                            .mapToInt(MemberCheckInRecord::getRoomNights)
                            .sum();

                    // 计算平均房费：总房费 / 总房晚数
                    if (totalRoomNights > 0) {
                        BigDecimal avgRoomFee = totalRoomFee.divide(BigDecimal.valueOf(totalRoomNights), 2, BigDecimal.ROUND_HALF_UP);
                        dto.setAvgRoomFee(avgRoomFee);
                    } else {
                        dto.setAvgRoomFee(BigDecimal.ZERO);
                    }
                } else {
                    // 如果没有入住记录，平均房费为0
                    dto.setAvgRoomFee(BigDecimal.ZERO);
                }

                return dto;
            }).collect(Collectors.toList());
        });
    }

    /**
     * 根据masterType，masterCode 以及时间范围，以及memberNoList 获取注册天数
     */
    protected List<BaseCheckDto> getMemberRegisterDaysRecordByMemberNo(Integer masterType, String masterCode, List<String> memberNoList, LocalDateTime start, LocalDateTime end) {
        return null;
    }

    protected Boolean calculateItem(Integer calculateType, String conditionValue, String factValue) {
        try {
            // 将字符串转换为 BigDecimal 进行比较
            BigDecimal conditionVal = new BigDecimal(conditionValue);
            BigDecimal factVal = new BigDecimal(factValue);

            switch (CalculateTypeEnum.getByType(calculateType)) {
                case GT_EQ: return factVal.compareTo(conditionVal) >= 0;
                case GT: return factVal.compareTo(conditionVal) > 0;
                case LT_EQ: return factVal.compareTo(conditionVal) <= 0;
                case LT: return factVal.compareTo(conditionVal) < 0;
                default: return false;
            }
        } catch (Exception e) {
           log.error("计算失败", e);
           return false;
        }
    }


}
