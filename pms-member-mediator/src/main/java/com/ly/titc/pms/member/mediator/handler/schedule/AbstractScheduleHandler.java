package com.ly.titc.pms.member.mediator.handler.schedule;

import com.alibaba.fastjson.JSONObject;
import com.ly.titc.pms.member.biz.MemberCardLevelChangeRecordBiz;
import com.ly.titc.pms.member.biz.MemberEventHappenInfoBiz;
import com.ly.titc.pms.member.com.constant.TurboMqTopic;
import com.ly.titc.pms.member.com.enums.CalculateTypeEnum;
import com.ly.titc.pms.member.com.lang.TwoTuple;
import com.ly.titc.pms.member.com.utils.ConfigCenterUtil;
import com.ly.titc.pms.member.dal.entity.po.MemberCardLevelChangeRecord;
import com.ly.titc.pms.member.dal.entity.po.MemberEventHappenInfo;
import com.ly.titc.pms.member.mediator.entity.dto.schedule.BaseCheckDto;
import com.ly.titc.pms.member.mediator.entity.dto.schedule.MemberScheduleMqDto;
import com.ly.titc.pms.member.mediator.entity.dto.schedule.ScheduleDataDto;
import com.ly.titc.springboot.mq.producer.TurboMQProducer;
import com.ly.watcher.common.utils.CollectionUtils;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.ly.titc.pms.member.com.constant.TurboMqTopicTag.MEMBER_EVENT;
import static com.ly.titc.pms.member.com.constant.TurboMqTopicTag.MEMBER_SCHEDULE;

/**
 * @Author：rui
 * @name：AbstractScheduleHandler
 * @Date：2024-11-21 14:29
 * @Filename：AbstractScheduleHandler
 */
@Slf4j
public abstract class AbstractScheduleHandler<Data extends ScheduleDataDto> {

    @Resource
    protected MemberCardLevelChangeRecordBiz memberCardLevelChangeRecordBiz;

    @Resource
    protected MemberEventHappenInfoBiz memberEventHappenInfoBiz;

    @Resource(type = TurboMQProducer.class)
    private TurboMQProducer producer;


    public void doSchedule() {
        // 定义一个线程池组
        ExecutorService executor = new ThreadPoolExecutor(
                Runtime.getRuntime().availableProcessors() * 5,
                Runtime.getRuntime().availableProcessors() * 5,
                10L,
                TimeUnit.SECONDS,
                new LinkedBlockingQueue<>()
        );

        List<String> blocCodes = ConfigCenterUtil.listBloc();
        for (String blocCode : blocCodes) {
            TwoTuple<Integer, String> masterByBloc = ConfigCenterUtil.getMasterByBloc(blocCode);
            Integer masterType = masterByBloc.getFirst();
            String masterCode = masterByBloc.getSecond();
            // 在内部执行事务代码快
            executor.execute(() -> doScheduleMainByMq(masterType, masterCode));
        }
    }

    public abstract void doScheduleMain(Integer masterType, String masterCode, List<Data> dataList, List<String> memberNos, List<MemberCardLevelChangeRecord> records);

    protected List<String> getNeedHandleMemberList(Integer masterType, String masterCode) {
        return memberEventHappenInfoBiz.listInYesterday(masterType, masterCode)
                .stream()
                .map(MemberEventHappenInfo::getMemberNo)
                .distinct()
                .collect(Collectors.toList());
    }

    public void doScheduleMain(Integer masterType, String masterCode, List<Data> dataList) {
        List<String> memberNo = getNeedHandleMemberList(masterType, masterCode);
        if (CollectionUtils.isEmpty(memberNo)) {
            log.info("没有需要处理的会员");
            return;
        }
        List<MemberCardLevelChangeRecord> records = records(masterType, masterCode);
        if (CollectionUtils.isEmpty(records)) {
            return;
        }
        // 获取所有符合条件的会员
        List<String> memberNos = records.stream().map(MemberCardLevelChangeRecord::getMemberNo).distinct().collect(Collectors.toList());

        // 分批处理，每批处理1000个会员
        int batchSize = 1000;
        int totalBatches = (int) Math.ceil((double) memberNos.size() / batchSize);

        for (int i = 0; i < totalBatches; i++) {
            int start = i * batchSize;
            int end = Math.min(start + batchSize, memberNos.size());
            List<String> batchMemberNos = memberNos.subList(start, end);
            doScheduleMain(masterType, masterCode, dataList, batchMemberNos, records);
        }
    }

    public void doScheduleMainByMq(Integer masterType, String masterCode) {
        List<String> memberNo = getNeedHandleMemberList(masterType, masterCode);
        if (CollectionUtils.isEmpty(memberNo)) {
            log.info("没有需要处理的会员");
            return;
        }
        for (String s : memberNo) {
            MemberScheduleMqDto msg = new MemberScheduleMqDto();
            msg.setMemberNo(s);
            msg.setMasterCode(masterCode);
            msg.setMasterType(masterType);
            msg.setAction(getAction());
            String str = JSONObject.toJSONString(msg);
            try {
                producer.sendMsgWithTag(TurboMqTopic.PMS_MEMBER_BPS_TOPIC, MEMBER_SCHEDULE, str);
            } catch (Exception e) {
                log.error("发送会员定时任务失败, topic:{}, tag:{}, msg:{}", TurboMqTopic.PMS_MEMBER_BPS_TOPIC, MEMBER_SCHEDULE, str);
            }
        }

    }

    public abstract void process(Integer masterType, String masterCode, String memberNo);

    /**
     * 获取类型
     *
     * @return
     */
    public abstract Integer getAction();

    public AbstractScheduleHandler() {
        ScheduleHandlerFactory.putHandler(this.getAction(), this);
    }

    protected AbstractScheduleHandler getHandler() {
        return ScheduleHandlerFactory.getHandler(this.getAction());
    }

    protected List<MemberCardLevelChangeRecord> records(Integer masterType, String masterCode) {
        return memberCardLevelChangeRecordBiz.getLevelChangeRecordByMaster(masterType, masterCode);
    }

    /**
     * 根据masterType，masterCode 以及时间范围，以及memberNoList 获取会员积分记录
     */
    protected List<BaseCheckDto> getMemberPointRecordByMemberNo(Integer masterType, String masterCode, List<String> memberNoList, LocalDateTime start, LocalDateTime end) {
        return null;
    }

    /**
     * 根据masterType，masterCode 以及时间范围，以及memberNoList 获取会员消费金额记录
     */
    protected List<BaseCheckDto> getMemberConsumptionRecordByMemberNo(Integer masterType, String masterCode, List<String> memberNoList, LocalDateTime start, LocalDateTime end) {
        return null;
    }

    /**
     * 根据masterType，masterCode 以及时间范围，以及memberNoList 获取会员充值金额
     */
    protected List<BaseCheckDto> getMemberRechargeRecordByMemberNo(Integer masterType, String masterCode, List<String> memberNoList, LocalDateTime start, LocalDateTime end) {
        return null;
    }

    /**
     * 根据masterType，masterCode 以及时间范围，以及memberNoList 获取会员入住次数
     */
    protected List<BaseCheckDto> getMemberCheckoutRecordByMemberNo(Integer masterType, String masterCode, List<String> memberNoList, LocalDateTime start, LocalDateTime end) {
        return null;
    }

    /**
     * 根据masterType，masterCode 以及时间范围，以及memberNoList 获取会员入住房晚
     */
    protected List<BaseCheckDto> getMemberStayRecordByMemberNo(Integer masterType, String masterCode, List<String> memberNoList, LocalDateTime start, LocalDateTime end) {
        return null;
    }

    /**
     * 根据masterType，masterCode 以及时间范围，以及memberNoList 获取会员未入住天数
     */
    protected List<BaseCheckDto> getMemberUnstayRecordByMemberNo(Integer masterType, String masterCode, List<String> memberNoList, LocalDateTime start, LocalDateTime end) {
        return null;
    }

    /**
     * 根据masterType，masterCode 以及时间范围，以及memberNoList 获取平均房费
     */
    protected List<BaseCheckDto> getMemberAvgRoomFeeRecordByMemberNo(Integer masterType, String masterCode, List<String> memberNoList, LocalDateTime start, LocalDateTime end) {
        return null;
    }

    /**
     * 根据masterType，masterCode 以及时间范围，以及memberNoList 获取注册天数
     */
    protected List<BaseCheckDto> getMemberRegisterDaysRecordByMemberNo(Integer masterType, String masterCode, List<String> memberNoList, LocalDateTime start, LocalDateTime end) {
        return null;
    }

    protected Boolean calculateItem(Integer calculateType, String conditionValue, String factValue) {
        try {
            // 将字符串转换为数值进行比较
            double conditionVal = Double.parseDouble(conditionValue);
            double factVal = Double.parseDouble(factValue);

            switch (CalculateTypeEnum.getByType(calculateType)) {
                case GT_EQ: return factVal >= conditionVal;
                case GT: return factVal > conditionVal;
                case LT_EQ: return factVal <= conditionVal;
                case LT: return factVal < conditionVal;
                default: return false;
            }
        } catch (NumberFormatException e) {
            // 如果无法转换为数值，则使用字符串比较
            switch (CalculateTypeEnum.getByType(calculateType)) {
                case GT_EQ: return factValue.compareTo(conditionValue) >= 0;
                case GT: return factValue.compareTo(conditionValue) > 0;
                case LT_EQ: return factValue.compareTo(conditionValue) <= 0;
                case LT: return factValue.compareTo(conditionValue) < 0;
                default: return false;
            }
        }
    }


}
