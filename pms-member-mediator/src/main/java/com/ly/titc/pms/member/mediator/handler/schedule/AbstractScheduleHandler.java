package com.ly.titc.pms.member.mediator.handler.schedule;

import com.alibaba.fastjson.JSONObject;
import com.ly.titc.pms.member.asset.dubbo.entity.response.point.MemberPointPeriodResp;
import com.ly.titc.pms.member.asset.dubbo.entity.response.point.MemberTotalPointResp;
import com.ly.titc.pms.member.asset.dubbo.entity.response.store.MemberStorePeriodResp;
import com.ly.titc.pms.member.biz.MemberCardLevelChangeRecordBiz;
import com.ly.titc.pms.member.biz.MemberCheckInRecordBiz;
import com.ly.titc.pms.member.biz.MemberEventHappenInfoBiz;
import com.ly.titc.pms.member.com.constant.TurboMqTopic;
import com.ly.titc.pms.member.com.enums.CalculateTypeEnum;
import com.ly.titc.pms.member.com.lang.TwoTuple;
import com.ly.titc.pms.member.com.utils.ConfigCenterUtil;
import com.ly.titc.pms.member.dal.entity.po.MemberCardLevelChangeRecord;
import com.ly.titc.pms.member.dal.entity.po.MemberCheckInRecord;
import com.ly.titc.pms.member.dal.entity.po.MemberEventHappenInfo;
import com.ly.titc.pms.member.entity.bo.PageMemberCheckInParamBo;
import com.ly.titc.pms.member.mediator.entity.dto.schedule.BaseCheckDto;
import com.ly.titc.pms.member.mediator.entity.dto.schedule.MemberScheduleMqDto;
import com.ly.titc.pms.member.mediator.entity.dto.schedule.ScheduleDataDto;
import com.ly.titc.pms.member.mediator.rpc.dubbo.asset.AssetDecorator;
import com.ly.titc.springboot.mq.producer.TurboMQProducer;
import com.ly.watcher.common.utils.CollectionUtils;
import com.ly.titc.pms.member.com.utils.PageUtil;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.ly.titc.pms.member.com.constant.TurboMqTopicTag.MEMBER_EVENT;
import static com.ly.titc.pms.member.com.constant.TurboMqTopicTag.MEMBER_SCHEDULE;

/**
 * @Author：rui
 * @name：AbstractScheduleHandler
 * @Date：2024-11-21 14:29
 * @Filename：AbstractScheduleHandler
 */
@Slf4j
public abstract class AbstractScheduleHandler<Data extends ScheduleDataDto> {

    @Resource
    protected MemberCardLevelChangeRecordBiz memberCardLevelChangeRecordBiz;

    @Resource
    protected MemberCheckInRecordBiz checkInRecordBiz;

    @Resource
    protected MemberEventHappenInfoBiz memberEventHappenInfoBiz;

    @Resource(type = TurboMQProducer.class)
    private TurboMQProducer producer;

    @Resource
    private AssetDecorator assetDecorator;


    public void doSchedule() {
        // 定义一个线程池组
        ExecutorService executor = new ThreadPoolExecutor(
                Runtime.getRuntime().availableProcessors() * 5,
                Runtime.getRuntime().availableProcessors() * 5,
                10L,
                TimeUnit.SECONDS,
                new LinkedBlockingQueue<>()
        );

        List<String> blocCodes = ConfigCenterUtil.listBloc();
        for (String blocCode : blocCodes) {
            TwoTuple<Integer, String> masterByBloc = ConfigCenterUtil.getMasterByBloc(blocCode);
            Integer masterType = masterByBloc.getFirst();
            String masterCode = masterByBloc.getSecond();
            // 在内部执行事务代码快
            executor.execute(() -> doScheduleMainByMq(masterType, masterCode));
        }
    }

    public abstract void doScheduleMain(Integer masterType, String masterCode, List<Data> dataList, List<String> memberNos, List<MemberCardLevelChangeRecord> records);

    protected List<String> getNeedHandleMemberList(Integer masterType, String masterCode) {
        return memberEventHappenInfoBiz.listInYesterday(masterType, masterCode)
                .stream()
                .map(MemberEventHappenInfo::getMemberNo)
                .distinct()
                .collect(Collectors.toList());
    }

    public void doScheduleMain(Integer masterType, String masterCode, List<Data> dataList) {
        List<String> memberNo = getNeedHandleMemberList(masterType, masterCode);
        if (CollectionUtils.isEmpty(memberNo)) {
            log.info("没有需要处理的会员");
            return;
        }
        List<MemberCardLevelChangeRecord> records = records(masterType, masterCode);
        if (CollectionUtils.isEmpty(records)) {
            return;
        }
        // 获取所有符合条件的会员
        List<String> memberNos = records.stream().map(MemberCardLevelChangeRecord::getMemberNo).distinct().collect(Collectors.toList());

        // 分批处理，每批处理1000个会员
        int batchSize = 1000;
        int totalBatches = (int) Math.ceil((double) memberNos.size() / batchSize);

        for (int i = 0; i < totalBatches; i++) {
            int start = i * batchSize;
            int end = Math.min(start + batchSize, memberNos.size());
            List<String> batchMemberNos = memberNos.subList(start, end);
            doScheduleMain(masterType, masterCode, dataList, batchMemberNos, records);
        }
    }

    public void doScheduleMainByMq(Integer masterType, String masterCode) {
        List<String> memberNo = getNeedHandleMemberList(masterType, masterCode);
        if (CollectionUtils.isEmpty(memberNo)) {
            log.info("没有需要处理的会员");
            return;
        }
        for (String s : memberNo) {
            MemberScheduleMqDto msg = new MemberScheduleMqDto();
            msg.setMemberNo(s);
            msg.setMasterCode(masterCode);
            msg.setMasterType(masterType);
            msg.setAction(getAction());
            String str = JSONObject.toJSONString(msg);
            try {
                producer.sendMsgWithTag(TurboMqTopic.PMS_MEMBER_BPS_TOPIC, MEMBER_SCHEDULE, str);
            } catch (Exception e) {
                log.error("发送会员定时任务失败, topic:{}, tag:{}, msg:{}", TurboMqTopic.PMS_MEMBER_BPS_TOPIC, MEMBER_SCHEDULE, str);
            }
        }

    }

    public abstract void process(Integer masterType, String masterCode, String memberNo);

    /**
     * 获取类型
     *
     * @return
     */
    public abstract Integer getAction();

    public AbstractScheduleHandler() {
        ScheduleHandlerFactory.putHandler(this.getAction(), this);
    }

    protected AbstractScheduleHandler getHandler() {
        return ScheduleHandlerFactory.getHandler(this.getAction());
    }

    protected List<MemberCardLevelChangeRecord> records(Integer masterType, String masterCode) {
        return memberCardLevelChangeRecordBiz.getLevelChangeRecordByMaster(masterType, masterCode);
    }

    /**
     * 根据masterType，masterCode 以及时间范围，以及memberNoList 获取会员积分记录
     */
    protected List<BaseCheckDto> getMemberPointRecordByMemberNo(Integer masterType, String masterCode, List<String> memberNoList, LocalDateTime start, LocalDateTime end) {
        // 将LocalDateTime转换为String格式，因为AssetDecorator的方法需要String参数
        String startDate = start.toLocalDate().toString() + " 00:00:00";
        String endDate = end.toLocalDate().toString() + " 23:59:59";
        // 调用资产服务获取会员积分统计数据
        MemberPointPeriodResp pointPeriodResp = assetDecorator.getTotalAccountPointsPeriodList(memberNoList, startDate, endDate);
        // 如果返回结果为空或没有数据，返回空列表
        if (pointPeriodResp == null || pointPeriodResp.getMemberPointPeriodList() == null) {
            return new ArrayList<>();
        }
        // 将积分数据转换为BaseCheckDto列表
        return pointPeriodResp.getMemberPointPeriodList().stream().map(item -> {
            BaseCheckDto dto = new BaseCheckDto();
            dto.setMemberNo(item.getMemberNo());
            // 设置总积分余额，如果为null则默认为0
            dto.setTotalScoreBalance(item.getTotalScore() != null ? item.getTotalScore() : 0);
            return dto;
        }).collect(Collectors.toList());
    }

    /**
     * 根据masterType，masterCode 以及时间范围，以及memberNoList 获取会员消费金额记录
     */
    protected List<BaseCheckDto> getMemberConsumptionRecordByMemberNo(Integer masterType, String masterCode, List<String> memberNoList, LocalDateTime start, LocalDateTime end) {
        List<BaseCheckDto> resultList = new ArrayList<>();
        // 如果会员列表为空，直接返回空列表
        if (CollectionUtils.isEmpty(memberNoList)) {
            return resultList;
        }
        // 先获取储值消费金额
        List<BaseCheckDto> rechargeList = getMemberRechargeRecordByMemberNo(masterType, masterCode, memberNoList, start, end);
        Map<String, BaseCheckDto> rechargeMap = rechargeList.stream()
                .collect(Collectors.toMap(BaseCheckDto::getMemberNo, dto -> dto));
        // 将LocalDateTime转换为String格式进行查询
        String startDate = start.toLocalDate().toString();
        String endDate = end.toLocalDate().toString();
        // 为每个会员查询入住记录并计算消费金额
        for (String memberNo : memberNoList) {
            try {
                // 使用分页查询方法获取该会员在指定时间范围内的入住记录
                PageMemberCheckInParamBo paramBo = new PageMemberCheckInParamBo();
                paramBo.setMemberNo(memberNo)
                       .setCheckInBeginTime(startDate)
                       .setCheckInEndTime(endDate);
                // 查询所有符合条件的入住记录
                List<MemberCheckInRecord> checkInRecords = PageUtil.queryAll((int pageIndex, int pageSize) ->
                        checkInRecordBiz.pageCheckInRecord(paramBo.setPageIndex(pageIndex).setPageSize(pageSize)), null);
                BaseCheckDto dto = new BaseCheckDto();
                dto.setMemberNo(memberNo);
                // 计算入住消费金额：房费 + 其他费用
                BigDecimal checkInExpense = BigDecimal.ZERO;
                if (!CollectionUtils.isEmpty(checkInRecords)) {
                    checkInExpense = checkInRecords.stream()
                            .map(record -> {
                                BigDecimal roomRate = record.getRoomRate() != null ? record.getRoomRate() : BigDecimal.ZERO;
                                BigDecimal otherRate = record.getOtherRate() != null ? record.getOtherRate() : BigDecimal.ZERO;
                                return roomRate.add(otherRate);
                            })
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                }

                // 获取储值消费金额
                BigDecimal rechargeExpense = BigDecimal.ZERO;
                BaseCheckDto rechargeDto = rechargeMap.get(memberNo);
                if (rechargeDto != null && rechargeDto.getTotalCapitalAmount() != null) {
                    rechargeExpense = rechargeDto.getTotalCapitalAmount();
                }
                // 总消费金额 = 入住消费 + 储值消费
                BigDecimal totalExpense = checkInExpense.add(rechargeExpense);
                dto.setExpenseAmount(totalExpense);
                resultList.add(dto);
            } catch (Exception e) {
                // 记录异常但继续处理其他会员
                log.warn("查询会员消费记录失败, memberNo: {}, error: {}", memberNo, e.getMessage());
                // 即使查询失败，也添加一个默认的记录
                BaseCheckDto dto = new BaseCheckDto();
                dto.setMemberNo(memberNo);
                dto.setExpenseAmount(BigDecimal.ZERO);
                // 尝试获取储值信息
                BaseCheckDto rechargeDto = rechargeMap.get(memberNo);
                if (rechargeDto != null) {
                    dto.setTotalScoreBalance(rechargeDto.getTotalScoreBalance());
                    dto.setTotalCapitalAmount(rechargeDto.getTotalCapitalAmount());
                    dto.setExpenseAmount(rechargeDto.getTotalCapitalAmount() != null ?
                            rechargeDto.getTotalCapitalAmount() : BigDecimal.ZERO);
                }
                resultList.add(dto);
            }
        }
        return resultList;
    }

    /**
     * 根据masterType，masterCode 以及时间范围，以及memberNoList 获取会员充值金额
     */
    protected List<BaseCheckDto> getMemberRechargeRecordByMemberNo(Integer masterType, String masterCode, List<String> memberNoList, LocalDateTime start, LocalDateTime end) {
        String startDate = start.toLocalDate().toString() + " 00:00:00";
        String endDate = end.toLocalDate().toString() + " 23:59:59";
        MemberStorePeriodResp resp = assetDecorator.getMemeberStorePeriodList(memberNoList, startDate, endDate);
        if (resp == null || resp.getMemberStorePeriodList() == null) {
            return new ArrayList<>();
        }
        // 将积分数据转换为BaseCheckDto列表
        return resp.getMemberStorePeriodList().stream().map(item -> {
            BaseCheckDto dto = new BaseCheckDto();
            dto.setMemberNo(item.getMemberNo());
            dto.setTotalCapitalAmount(item.getTotalAmountResp() != null ? item.getTotalAmountResp().getTotalCapitalAmount() : BigDecimal.ZERO);
            return dto;
        }).collect(Collectors.toList());
    }

    /**
     * 根据masterType，masterCode 以及时间范围，以及memberNoList 获取会员入住次数
     */
    protected List<BaseCheckDto> getMemberCheckoutRecordByMemberNo(Integer masterType, String masterCode, List<String> memberNoList, LocalDateTime start, LocalDateTime end) {
        return null;
    }

    /**
     * 根据masterType，masterCode 以及时间范围，以及memberNoList 获取会员入住房晚
     */
    protected List<BaseCheckDto> getMemberStayRecordByMemberNo(Integer masterType, String masterCode, List<String> memberNoList, LocalDateTime start, LocalDateTime end) {
        return null;
    }

    /**
     * 根据masterType，masterCode 以及时间范围，以及memberNoList 获取会员未入住天数
     */
    protected List<BaseCheckDto> getMemberUnstayRecordByMemberNo(Integer masterType, String masterCode, List<String> memberNoList, LocalDateTime start, LocalDateTime end) {
        return null;
    }

    /**
     * 根据masterType，masterCode 以及时间范围，以及memberNoList 获取平均房费
     */
    protected List<BaseCheckDto> getMemberAvgRoomFeeRecordByMemberNo(Integer masterType, String masterCode, List<String> memberNoList, LocalDateTime start, LocalDateTime end) {
        return null;
    }

    /**
     * 根据masterType，masterCode 以及时间范围，以及memberNoList 获取注册天数
     */
    protected List<BaseCheckDto> getMemberRegisterDaysRecordByMemberNo(Integer masterType, String masterCode, List<String> memberNoList, LocalDateTime start, LocalDateTime end) {
        return null;
    }

    protected Boolean calculateItem(Integer calculateType, String conditionValue, String factValue) {
        try {
            // 将字符串转换为 BigDecimal 进行比较
            BigDecimal conditionVal = new BigDecimal(conditionValue);
            BigDecimal factVal = new BigDecimal(factValue);

            switch (CalculateTypeEnum.getByType(calculateType)) {
                case GT_EQ: return factVal.compareTo(conditionVal) >= 0;
                case GT: return factVal.compareTo(conditionVal) > 0;
                case LT_EQ: return factVal.compareTo(conditionVal) <= 0;
                case LT: return factVal.compareTo(conditionVal) < 0;
                default: return false;
            }
        } catch (Exception e) {
           log.error("计算失败", e);
           return false;
        }
    }


}
